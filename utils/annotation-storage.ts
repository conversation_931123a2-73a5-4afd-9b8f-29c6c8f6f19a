import { DataItem } from '@/services/collectionService'
import { MediaTypeHelper } from '@/types'

/**
 * 标注数据存储管理工具类
 * 用于统一管理标注页面的 sessionStorage 数据存储和读取
 */
export class AnnotationStorageManager {
  private static readonly STORAGE_KEY = 'annotationItems'

  /**
   * 存储标注数据到 sessionStorage
   * @param items 要存储的数据项数组
   */
  static store(items: DataItem[]): void {
    try {
      sessionStorage.setItem(this.STORAGE_KEY, JSON.stringify(items))
    } catch (error) {
      console.error('存储标注数据失败:', error)
      throw new Error('存储标注数据失败')
    }
  }

  /**
   * 从 sessionStorage 读取标注数据
   * @returns 标注数据项数组，如果没有数据则返回 null
   */
  static load(): DataItem[] | null {
    try {
      const storedItems = sessionStorage.getItem(this.STORAGE_KEY)
      if (!storedItems) {
        return null
      }

      const items = JSON.parse(storedItems) as DataItem[]
      return Array.isArray(items) && items.length > 0 ? items : null
    } catch (error) {
      console.error('读取标注数据失败:', error)
      return null
    }
  }

  /**
   * 清除 sessionStorage 中的标注数据
   */
  static clear(): void {
    try {
      sessionStorage.removeItem(this.STORAGE_KEY)
    } catch (error) {
      console.error('清除标注数据失败:', error)
    }
  }

  /**
   * 验证标注数据的有效性
   * @param items 要验证的数据项数组
   * @returns 验证结果对象
   */
  static validate(items: DataItem[]): {
    isValid: boolean
    error?: string
    mediaType?: 'image' | 'video'
  } {
    if (!items || items.length === 0) {
      return {
        isValid: false,
        error: '没有找到需要标注的数据'
      }
    }

    // 检查是否只有一种媒体类型
    const mediaTypes = Array.from(new Set(items.map(item => item.itemType)))
    if (mediaTypes.length > 1) {
      return {
        isValid: false,
        error: '只能选择一种类型的数据，不能同时选择图片和视频'
      }
    }

    const mediaType = mediaTypes[0]
    const isImage = MediaTypeHelper.isImage(mediaType)
    const isVideo = MediaTypeHelper.isVideo(mediaType)

    if (!isImage && !isVideo) {
      return {
        isValid: false,
        error: '不支持的媒体类型'
      }
    }

    // 视频类型只能选择一个
    if (isVideo && items.length > 1) {
      return {
        isValid: false,
        error: '视频类型只能选择一个进行标注'
      }
    }

    return {
      isValid: true,
      mediaType: isImage ? 'image' : 'video'
    }
  }

  /**
   * 获取第一个视频项（仅用于视频标注）
   * @param items 数据项数组
   * @returns 第一个视频项，如果没有则返回 null
   */
  static getFirstVideoItem(items: DataItem[]): DataItem | null {
    const validation = this.validate(items)
    if (!validation.isValid || validation.mediaType !== 'video') {
      return null
    }
    return items[0]
  }

  /**
   * 检查是否存在有效的标注数据
   * @returns 是否存在有效数据
   */
  static hasValidData(): boolean {
    const items = this.load()
    if (!items) return false

    const validation = this.validate(items)
    return validation.isValid
  }

  /**
   * 获取媒体类型（不加载完整数据）
   * @returns 媒体类型或 null
   */
  static getMediaType(): 'image' | 'video' | null {
    const items = this.load()
    if (!items) return null

    const validation = this.validate(items)
    return validation.isValid ? validation.mediaType! : null
  }

  /**
   * 安全加载标注数据，包含错误处理
   * @param onError 错误处理回调函数
   * @returns 加载结果对象
   */
  static safeLoad(onError?: (error: string) => void): {
    success: boolean
    data?: DataItem[]
    error?: string
    mediaType?: 'image' | 'video'
  } {
    const items = this.load()

    if (!items) {
      const error = '没有找到需要标注的数据'
      onError?.(error)
      return { success: false, error }
    }

    const validation = this.validate(items)
    if (!validation.isValid) {
      onError?.(validation.error!)
      return { success: false, error: validation.error }
    }

    return {
      success: true,
      data: items,
      mediaType: validation.mediaType
    }
  }

  /**
   * 获取数据项数量
   * @returns 数据项数量，如果没有数据则返回 0
   */
  static getItemCount(): number {
    const items = this.load()
    return items ? items.length : 0
  }

  /**
   * 检查指定的数据项是否存在
   * @param itemId 数据项ID
   * @returns 是否存在
   */
  static hasItem(itemId: string): boolean {
    const items = this.load()
    return items ? items.some(item => item.itemId === itemId) : false
  }

  /**
   * 获取指定的数据项
   * @param itemId 数据项ID
   * @returns 数据项或 null
   */
  static getItem(itemId: string): DataItem | null {
    const items = this.load()
    return items ? items.find(item => item.itemId === itemId) || null : null
  }
}
