'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { nanoid } from 'nanoid'
import { VideoPlayer } from '@/components/ui/video-annotator/VideoPlayer'
import { AnnotationList, VideoAnnotation } from '@/components/ui/video-annotator/AnnotationList'
import { emotionShortcuts, EmotionType } from '@/components/ui/image-annotator/ShortcutHints'
import { ArrowLeft, Clock } from 'lucide-react'
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { ClipPreview } from '@/components/ui/video-annotator/ClipPreview'
import { toast } from '@/components/ui/use-toast'
import { DataItem } from '@/services/collectionService'
import { AnnotationStorageManager } from '@/utils/annotation-storage'

// 统一使用这个视频地址
const videoUrl = 'https://www.w3schools.com/html/mov_bbb.mp4'

export default function VideoAnnotationPage() {
  const router = useRouter()
  const [videoItem, setVideoItem] = useState<DataItem | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [annotations, setAnnotations] = useState<VideoAnnotation[]>([])
  const [isHighlightMode, setIsHighlightMode] = useState(false)
  const [highlightStartTime, setHighlightStartTime] = useState<number | null>(null)
  const [currentTime, setCurrentTime] = useState(0)
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [pendingAnnotation, setPendingAnnotation] = useState<{
    startTime: number
    endTime: number
    emotion: string
  } | null>(null)

  // 添加视频帧率信息，用于计算帧位置
  const [videoFrameRate, setVideoFrameRate] = useState(29.97)

  // 添加对视频播放控制的引用
  const [autoPlayVideo, setAutoPlayVideo] = useState(false)
  const [autoPauseVideo, setAutoPauseVideo] = useState(false)

  // 从会话存储中获取视频数据
  useEffect(() => {
    const result = AnnotationStorageManager.safeLoad((error) => {
      toast({
        title: '数据错误',
        description: error,
        variant: 'destructive',
      })
      router.push('/admin/annotation/workbench')
    })

    if (result.success && result.data && result.mediaType === 'video') {
      // 获取第一个视频项（视频标注一次只能标注一个视频）
      const videoItem = AnnotationStorageManager.getFirstVideoItem(result.data)
      if (videoItem) {
        setVideoItem(videoItem)
        setIsLoading(false)
      } else {
        toast({
          title: '数据错误',
          description: '无法获取视频数据',
          variant: 'destructive',
        })
        router.push('/admin/annotation/workbench')
      }
    } else if (result.success && result.mediaType !== 'video') {
      toast({
        title: '数据类型错误',
        description: '当前页面只能标注视频数据',
        variant: 'destructive',
      })
      router.push('/admin/annotation/workbench')
    }
  }, [router])

  const handleFrameRateChange = (rate: number) => {
    setVideoFrameRate(rate)
  }

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!isDialogOpen && e.code === 'KeyA' && !isHighlightMode) {
        e.preventDefault()
        setIsHighlightMode(true)
        setHighlightStartTime(currentTime)
        // 设置自动播放标记
        setAutoPlayVideo(true)
      } else if (!isDialogOpen && e.code === 'Enter' && isHighlightMode) {
        e.preventDefault()
        if (highlightStartTime !== null) {
          // 设置自动暂停标记
          setAutoPauseVideo(true)

          setPendingAnnotation({
            startTime: highlightStartTime,
            endTime: currentTime,
            emotion: Object.values(emotionShortcuts)[0],
          })
          setIsDialogOpen(true)
        }
        setIsHighlightMode(false)
        setHighlightStartTime(null)
      } else if (!isDialogOpen && e.code === 'Escape' && isHighlightMode) {
        // 添加按ESC取消标注模式的处理
        e.preventDefault()
        setIsHighlightMode(false)
        setHighlightStartTime(null)
        // 设置自动暂停标记
        setAutoPauseVideo(true)
      } else if (isDialogOpen && pendingAnnotation) {
        const key = e.key.toLowerCase()
        if (key in emotionShortcuts) {
          e.preventDefault()
          setPendingAnnotation({
            ...pendingAnnotation,
            emotion: emotionShortcuts[key as EmotionType],
          })
        } else if (e.code === 'Enter') {
          e.preventDefault()
          handleDialogConfirm()
        } else if (e.code === 'Escape') {
          e.preventDefault()
          handleDialogCancel()
        }
      }
    }

    window.addEventListener('keydown', handleKeyDown)
    return () => window.removeEventListener('keydown', handleKeyDown)
  }, [isHighlightMode, highlightStartTime, currentTime, isDialogOpen, pendingAnnotation])

  const handleEmotionChange = (id: string, emotion: string) => {
    setAnnotations(prev =>
      prev.map(annotation => (annotation.id === id ? { ...annotation, emotion } : annotation))
    )
  }

  const handleDeleteAnnotation = (id: string) => {
    setAnnotations(prev => prev.filter(annotation => annotation.id !== id))
  }

  // 更新弹窗确认函数，使用更新后的开始和结束时间
  const handleDialogConfirm = () => {
    if (pendingAnnotation) {
      const newAnnotation: VideoAnnotation = {
        id: nanoid(),
        startTime: pendingAnnotation.startTime,
        endTime: pendingAnnotation.endTime,
        emotion: pendingAnnotation.emotion,
        previewUrl: undefined,
      }
      setAnnotations(prev => [...prev, newAnnotation])
      setIsDialogOpen(false)
      setPendingAnnotation(null)
    }
  }

  const handleDialogCancel = () => {
    setIsDialogOpen(false)
    setPendingAnnotation(null)
  }

  const handleEmotionSelect = (emotion: string) => {
    if (pendingAnnotation) {
      setPendingAnnotation({
        ...pendingAnnotation,
        emotion,
      })
    }
  }

  // 处理开始/结束帧调整
  const handleAdjustStartFrame = (frame: number) => {
    if (!pendingAnnotation) return

    const newStartTime = frame / videoFrameRate
    // 确保开始时间不超过结束时间
    if (newStartTime < pendingAnnotation.endTime) {
      setPendingAnnotation({
        ...pendingAnnotation,
        startTime: newStartTime,
      })
    }
  }

  const handleAdjustEndFrame = (frame: number) => {
    if (!pendingAnnotation) return

    const newEndTime = frame / videoFrameRate
    // 确保结束时间大于开始时间
    if (newEndTime > pendingAnnotation.startTime) {
      setPendingAnnotation({
        ...pendingAnnotation,
        endTime: newEndTime,
      })
    }
  }

  const handleBackToWorkbench = () => {
    router.push('/admin/annotation/workbench')
  }

  const handleSubmitAnnotations = () => {
    if (annotations.length === 0) {
      toast({
        title: '没有标注数据',
        description: '请至少添加一个情绪标注',
        variant: 'destructive',
      })
      return
    }

    if (videoItem) {
      // 将所有标注组合为一个结果
      const result = {
        ...videoItem,
        annotations: annotations,
      }

      // TODO: 将标注结果发送到后端
      console.log('视频标注完成，结果:', result)

      toast({
        title: '标注完成',
        description: `成功添加了 ${annotations.length} 个标注`,
      })

      // 返回工作台
      router.push('/admin/annotation/workbench')
    }
  }

  if (isLoading) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary mx-auto mb-4"></div>
          <p>加载视频数据中...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="flex flex-col h-[calc(100vh-4rem)]">
      {/* 顶部导航栏 */}
      <div className="border-b bg-background py-3 px-6">
        <div className="flex items-center justify-between">
          <Button variant="ghost" size="sm" onClick={handleBackToWorkbench}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            返回工作台
          </Button>
          <h1 className="text-xl font-semibold">视频情绪标注</h1>
          <Button size="sm" onClick={handleSubmitAnnotations}>
            提交标注
          </Button>
        </div>
      </div>

      <div className="flex flex-1 overflow-hidden">
        <div className="w-[70%] border-r relative flex flex-col">
          <div className="flex-1 relative">
            <VideoPlayer
              src={videoUrl}
              highlightMode={isHighlightMode}
              onFrameChange={setCurrentTime}
              autoPlay={autoPlayVideo}
              autoPause={autoPauseVideo}
              onFrameRateChange={handleFrameRateChange}
              onPlayStateChange={isPlaying => {
                // 重置自动播放/暂停标记
                if (isPlaying) setAutoPlayVideo(false)
                if (!isPlaying) setAutoPauseVideo(false)
              }}
            />
            {isHighlightMode && (
              <div className="absolute top-4 left-1/2 transform -translate-x-1/2 bg-amber-50 border-amber-200 border text-amber-800 p-3 flex items-center justify-center gap-2 animate-pulse">
                <Clock className="h-5 w-5" />
                <span className="font-medium">
                  正在选择片段... 按{' '}
                  <kbd className="px-1.5 py-0.5 bg-background rounded border">Enter</kbd> 确认 或按{' '}
                  <kbd className="px-1.5 py-0.5 bg-background rounded border">Esc</kbd> 取消
                </span>
              </div>
            )}
          </div>
        </div>

        <div className="w-[30%] flex flex-col overflow-hidden">
          <div className="sticky top-0 bg-background p-4 border-b z-10 flex items-center justify-between">
            <h2 className="text-lg font-semibold">标注内容</h2>
            <div className="bg-muted p-2 text-sm flex items-center justify-center gap-4 border-b">
              <span className="flex items-center gap-1">
                <kbd className="px-2 py-1 bg-background rounded border">A</kbd>
                <span>开始标注</span>
              </span>
              <span className="flex items-center gap-1">
                <kbd className="px-2 py-1 bg-background rounded border">Enter</kbd>
                <span>完成标注</span>
              </span>
              <span className="flex items-center gap-1">
                <kbd className="px-2 py-1 bg-background rounded border">Esc</kbd>
                <span>取消标注</span>
              </span>
            </div>
          </div>
          <div className="flex-1 overflow-y-auto">
            <AnnotationList
              annotations={annotations}
              onEmotionChange={handleEmotionChange}
              onDelete={handleDeleteAnnotation}
            />
          </div>
        </div>
      </div>

      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="sm:max-w-5xl">
          <DialogHeader>
            <DialogTitle>为视频片段标注情绪</DialogTitle>
          </DialogHeader>
          {pendingAnnotation && (
            <div className="py-4">
              <div className="grid grid-cols-[2fr_1fr] gap-6">
                {/* 左侧: 视频预览（放大版） */}
                <div className="bg-black rounded-lg overflow-hidden">
                  <ClipPreview
                    src={videoUrl}
                    startTime={pendingAnnotation.startTime}
                    endTime={pendingAnnotation.endTime}
                    frameRate={videoFrameRate}
                    onStartFrameChange={handleAdjustStartFrame}
                    onEndFrameChange={handleAdjustEndFrame}
                  />
                </div>

                {/* 右侧: 所有控制元素和情绪选择 */}
                <div className="space-y-6">
                  {/* 帧控制和片段信息 */}
                  <div className="space-y-4">
                    <div className="font-medium">片段控制:</div>

                    {/* 开始帧控制 */}
                    <div className="space-y-2">
                      <h3 className="text-sm font-medium">开始帧:</h3>
                      <div className="flex gap-2 items-center">
                        <input
                          type="number"
                          className="w-20 h-8 px-2 border rounded text-sm"
                          value={Math.floor(pendingAnnotation.startTime * videoFrameRate)}
                          onChange={e => {
                            const frame = parseInt(e.target.value)
                            if (!isNaN(frame)) {
                              handleAdjustStartFrame(frame)
                            }
                          }}
                        />
                        <span className="text-sm">
                          时间: {pendingAnnotation.startTime.toFixed(3)}s
                        </span>
                      </div>
                    </div>

                    {/* 结束帧控制 */}
                    <div className="space-y-2">
                      <h3 className="text-sm font-medium">结束帧:</h3>
                      <div className="flex gap-2 items-center">
                        <input
                          type="number"
                          className="w-20 h-8 px-2 border rounded text-sm"
                          value={Math.floor(pendingAnnotation.endTime * videoFrameRate)}
                          onChange={e => {
                            const frame = parseInt(e.target.value)
                            if (!isNaN(frame)) {
                              handleAdjustEndFrame(frame)
                            }
                          }}
                        />
                        <span className="text-sm">
                          时间: {pendingAnnotation.endTime.toFixed(3)}s
                        </span>
                      </div>
                    </div>

                    <div className="bg-muted p-3 rounded flex flex-col space-y-1">
                      <span className="text-sm">
                        片段长度:{' '}
                        {(pendingAnnotation.endTime - pendingAnnotation.startTime).toFixed(3)}s
                      </span>
                      <span className="text-sm">
                        约{' '}
                        {Math.round(
                          (pendingAnnotation.endTime - pendingAnnotation.startTime) * videoFrameRate
                        )}{' '}
                        帧
                      </span>
                    </div>
                  </div>

                  {/* 情绪标签选择 */}
                  <div className="space-y-3">
                    <div className="font-medium">选择情绪标签:</div>
                    <div className="grid grid-cols-3 gap-2 overflow-y-auto pr-1">
                      {Object.entries(emotionShortcuts).map(([key, emotion]) => {
                        const isSelected = pendingAnnotation.emotion === emotion
                        return (
                          <Button
                            key={key}
                            variant={isSelected ? 'default' : 'outline'}
                            className="justify-start text-left"
                            onClick={() => handleEmotionSelect(emotion)}
                          >
                            <kbd
                              className={`mr-2 px-1.5 py-0.5 rounded border text-xs ${
                                isSelected
                                  ? 'bg-primary-foreground border-primary-foreground text-primary'
                                  : 'bg-muted border-border text-muted-foreground'
                              }`}
                            >
                              {key}
                            </kbd>
                            {emotion}
                          </Button>
                        )
                      })}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
          <DialogFooter className="sm:justify-between">
            <Button variant="outline" onClick={handleDialogCancel}>
              取消 (ESC)
            </Button>
            <Button onClick={handleDialogConfirm}>确认 (Enter)</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
