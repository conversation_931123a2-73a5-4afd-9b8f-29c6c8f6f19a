import { Card, CardContent } from '@/components/ui/card'
import { Progress } from '@radix-ui/react-progress'
import { CheckCircle } from 'lucide-react'
import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'

interface CurrentAnnotationInfoProps {
  total: number
  currentIndex: number
  currentExpressionType: string | null
  finishedCount: number
  handleSubmit: () => void
}

export function CurrentAnnotationInfo({
  total,
  currentIndex,
  currentExpressionType,
  finishedCount,
  handleSubmit,
}: CurrentAnnotationInfoProps) {
  const [completionPercentage, setCompletionPercentage] = useState(0)
  useEffect(() => {
    const percentage = (finishedCount / total) * 100
    setCompletionPercentage(percentage)
  }, [finishedCount, total])
  return <Card>
    <CardContent className="p-4">
      <h2 className="mb-2 font-semibold">当前图片信息</h2>
      <div className="text-sm text-muted-foreground">
        <p>
          图片 {currentIndex + 1} / {total}
        </p>
        <p className="mt-1">
          标注状态:{' '}
          {currentExpressionType ? (
            <span className="text-green-500 font-medium">
              已标注为 &#34;{currentExpressionType}&#34;
            </span>
          ) : (
            <span className="text-yellow-500 font-medium">未标注</span>
          )}
        </p>
      </div>
      <div className="mt-4">
        <Progress value={completionPercentage} className="h-2" />
        <p className="mt-2 text-sm text-muted-foreground">
          已完成 {Math.round(completionPercentage)}%
        </p>
      </div>
      {completionPercentage === 100 && (
        <Button className="w-full mt-4" onClick={handleSubmit}>
          <CheckCircle className="mr-2 h-4 w-4" />
          提交标注
        </Button>
      )}
    </CardContent>
  </Card>
}
